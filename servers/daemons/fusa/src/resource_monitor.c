#define ZF_LOG_LEVEL CONFIG_FUSA_LOG_LEVEL
#include <fusa/gen_config.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sysstats/stats.h>

#include <safehub.h>
#include <resource_monitor.h>

static uint64_t idle_slice[CONFIG_MAX_NUM_NODES] = {0};
static uint64_t idle_old_slice[CONFIG_MAX_NUM_NODES] = {0};
static uint64_t core_slice[CONFIG_MAX_NUM_NODES] = {0};
static uint64_t core_old_slice[CONFIG_MAX_NUM_NODES] = {0};

typedef uint16_t percent_t; // real percentage * 100

static percent_t get_core_load(int core)
{
    uint64_t idle_load =  idle_slice[core] - idle_old_slice[core];
    uint64_t core_load =  core_slice[core] - core_old_slice[core];

    if (core_load == 0)
        return 0;

    return (100 - 100 * idle_load / core_load);
}

/**
 * @brief Get the CPU usage of each core
 * @param[in] void
 * @return error code
 */
static int cpu_check_threshold(void)
{
    int err = 0;
    int ret_val = 0;
    percent_t cpu_load[CONFIG_MAX_NUM_NODES] = { 0 };

    for (int i = 0; i < CONFIG_MAX_NUM_NODES; i++) {
        err = sys_stats_get_cpu_timeslice(i, &core_slice[i]);
        if (err) {
            ret_val = err;
            continue;
        }

        err = sys_stats_get_thread_timeslice(0, PSV_IDLE_TID_START + i, &idle_slice[i]);
        if (err) {
            ret_val = err;
            continue;
        }

        // real cpu percentage * 100
        cpu_load[i] = get_core_load(i);

        if (cpu_load[i] > SAFETY_CPU_THRESHOLD) {
            struct safety_notify_msg msg = {0};
            msg.code = (uint32_t)ERR_CPU_OT;
            msg.extend[0] = cpu_load[i];
            msg.extend[1] = i;

            safehub_error_report(msg);
        }

        core_old_slice[i] = core_slice[i];
        idle_old_slice[i] = idle_slice[i];
        ZF_LOGV("Fusa: Core %d,  %d%%", i, cpu_load[i]);
    }

    return ret_val;
}

/**
 * @brief Get the system memory usage
 * @param[in] void
 * @return error code
 */
static int mem_check_threshold(void)
{
    int err = 0;
    struct sys_mem_stats sys_mem = { 0 };
    int mem_load = 0;

    err = sys_stats_get_sys_mem(&sys_mem);
    if (err) return err;

    ZF_LOGV("Fusa: total  %ld MB, avail %ld MB, free %ld MB, used %ld MB, cached %ld MB\n", sys_mem.total_usable / (1024 * 1024),
            sys_mem.available / (1024 * 1024), sys_mem.remaining / (1024 * 1024),
            (sys_mem.total_usable - sys_mem.available) / (1024 * 1024),
            (sys_mem.n_cached_4k * BIT(PAGE_BITS_4K) + sys_mem.n_cached_2m * BIT(PAGE_BITS_2M)) / (1024 * 1024));

    if (sys_mem.total_usable == 0) return FUSA_ERROR;

    mem_load = (sys_mem.total_usable - sys_mem.available) * 100 / sys_mem.total_usable;
    ZF_LOGV("Fusa: memory usage is %d%%\n", mem_load);

    if (mem_load > SAFETY_MEM_THRESHOLD) {
        struct safety_notify_msg msg = {0};
        msg.code = (uint32_t)ERR_MEM_OT;
        msg.extend[0] = mem_load;

        safehub_error_report(msg);
    }

    return err;
}

int fusa_resource_monitor_init(void)
{
    int err = 0;

    err = sys_stats_init();

    return err;
}

int fusa_resource_monitor_func(void)
{
    int err = 0;

    err = mem_check_threshold();
    if (err) ZF_LOGE("Fusa: memory monitor failed (err=%d)", err);

    err = cpu_check_threshold();
    if (err) ZF_LOGE("Fusa: cpu monitor failed (err=%d)", err);

    return err;
}
