#/daemons/fusa/CMakeLists
project(fusa C)

set(configure_string "")

if ("${CMAKE_BUILD_TYPE}" STREQUAL "Release")
    set(fusa_log_level "4")
else()
    set(fusa_log_level "2")
endif()

config_string(
    FusaLogLevel
    FUSA_LOG_LEVEL
    "Log Level Settings \
      1 - Verbose, 2 - Debug, 3 - Info, 4 - Warn, 5 - <PERSON><PERSON><PERSON>, Fatal - 0xFFFF, None - 0xFFFF"
    DEFAULT
    ${fusa_log_level}
    UNQUOTE
)

if ("${PLATFORM}" STREQUAL "qemu-arm-virt")
    set(fusa_chances 10)
else()
    set(fusa_chances 5)
endif()

config_string(
    FusaChances
    FUSA_CHANCES
    "Number of chances we should give before declaring a server is dead"
    DEFAULT
    ${fusa_chances}
    UNQUOTE
)

config_option(
    SSMEnableDump
    SSM_ENABLE_CORE_DUMP
    "Enable core dump of failed server"
    DEFAULT
    OFF
)

config_option(
    SSMSoftReset
    SSM_SOFT_RESET
    "Enable soft-set of only the A-core when a service fails"
    DEFAULT
    OFF
)

config_string(
    SsmSoftResetTimeOut
    SOFT_RESET_TIMEOUT
    "Wait time before a soft reboot"
    DEFAULT
    5
    UNQUOTE
)

config_option(
    EnableFusaBIValidation
    FUSA_VALIDATE_BOOT_INFO
    "Validate the boot information used by critical servers"
    DEPENDS EnableFusa
    DEFAULT
    OFF
)

config_string(
    SafehubWdgName
    SAFEHUB_WDG_NAME
    "Safehub soft watchdog name"
    DEFAULT "sw_wd_safehub"
)

config_option(
    SafehubEnableWdg
    SAFEHUB_ENABLE_SWDG
    "Enable safe hub software watchdog"
    DEFAULT
    OFF
)

config_option(
    EnablePitSleep
    ENABLE_PIT_SLEEP
    "Enable fusa sleep with PIT"
    DEFAULT
    OFF
)

if (TestStressPOSIX)
    config_option(
        DisableFusaTestApp
	DISABLE_FUSA_TEST_APP
	"Do not load the fusa test app"
	DEFAULT
	ON
    )
endif()

if ("${TestFusaBootVerify}" STREQUAL "FI_bootParam")
    config_option(TestSSMBootFault FUSA_SSM_FAULT_INJECTION_TEST
      "Inject errors to boot information"
      DEPENDS EnableFusaSSM
      DEFAULT ON
    )
endif()

add_config_library(fusa "${configure_string}")

file(GLOB files
  src/*.c
)

add_executable(fusa EXCLUDE_FROM_ALL ${files} ${MA_WRAPPER_SRC})

target_include_directories(fusa
  PUBLIC
  ${CMAKE_SOURCE_DIR}/libs/os_libs/libsysstats/include
  ${CMAKE_SOURCE_DIR}/servers/proc/include
  PRIVATE include
)

target_link_libraries(fusa
  PUBLIC
      muslc
      vsys
      core
      sysstats

  PRIVATE
    fusa_Config
    proc_Config
)

if (EnablePitSleep)
    target_link_libraries(fusa PUBLIC nxp_pit)
endif()

