#pragma once

#include <stdbool.h>

#define  FUSA_ERROR                    (1)
// Safety CPU threshold: cpu usage output is real cpu percentage * 100
#define SAFETY_CPU_THRESHOLD           (80)
// Safety memory threshold: memory output is real cpu percentage * 100
#define SAFETY_MEM_THRESHOLD           (80)

// Resource monitoring types for n-sigma algorithm
typedef enum {
    MONITOR_CPU_USAGE = 0,
    MONITOR_MEM_USAGE,
    MONITOR_PROC_MEM,
    MONITOR_IO_LATENCY,
    MONITOR_IRQ_COUNT,
    MONITOR_FS_MEMORY,
    MONITOR_TYPE_MAX
} monitor_type_t;

// Core resource monitoring functions
int fusa_resource_monitor_func(void);
int fusa_resource_monitor_init(void);

// N-sigma algorithm configuration functions
int fusa_nsigma_set_enabled(monitor_type_t type, bool enabled);
int fusa_nsigma_set_sigma(monitor_type_t type, double sigma);
int fusa_nsigma_get_stats(monitor_type_t type, double *mean, double *std_dev, int *sample_count);
